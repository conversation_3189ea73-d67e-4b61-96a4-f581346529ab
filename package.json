{"name": "openpod", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.6", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.363.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "socket.io-client": "^4.7.5", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.1"}, "devDependencies": {"@types/node": "^20.11.30", "@types/react": "^18.2.69", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5.4.5", "vite": "^5.2.6"}}
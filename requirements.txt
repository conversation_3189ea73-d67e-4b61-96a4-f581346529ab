aiohappyeyeballs==2.4.3
aiohttp==3.11.2
aiosignal==1.3.1
alabaster==1.0.0
annotated-types==0.7.0
anyio==4.6.2.post1
attrs==24.2.0
babel==2.16.0
beautifulsoup4==4.12.3
bidict==0.23.1
bleach==6.2.0
blinker==1.9.0
cachetools==5.5.0
certifi==2024.8.30
charset-normalizer==3.4.0
click==8.1.7
Cython==3.0.11
dataclasses-json==0.6.7
defusedxml==0.7.1
distro==1.9.0
docstring_parser==0.16
docutils==0.21.2
edge-tts==6.1.15
elevenlabs==1.12.1
execnet==2.1.1
fastjsonschema==2.20.0
ffmpeg==1.4
filelock==3.16.1
Flask==3.0.0
Flask-Cors==5.0.0
Flask-SocketIO==5.4.1
frozenlist==1.5.0
fsspec==2024.10.0
fuzzywuzzy==0.18.0
google-ai-generativelanguage==0.6.10
google-api-core==2.23.0
google-api-python-client==2.153.0
google-auth==2.36.0
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.72.0
google-cloud-bigquery==3.27.0
google-cloud-core==2.4.1
google-cloud-resource-manager==1.13.1
google-cloud-storage==2.18.2
google-cloud-texttospeech==2.21.1
google-crc32c==1.6.0
google-generativeai==0.8.3
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
grpc-google-iam-v1==0.13.1
grpcio==1.67.1
grpcio-status==1.67.1
gunicorn==23.0.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.27.2
httpx-sse==0.4.0
huggingface-hub==0.26.2
idna==3.10
imagesize==1.4.1
importlib_metadata==8.5.0
iniconfig==2.0.0
itsdangerous==2.2.0
Jinja2==3.1.4
jiter==0.7.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
langchain==0.3.7
langchain-community==0.3.7
langchain-core==0.3.19
langchain-google-genai==2.0.4
langchain-google-vertexai==2.0.7
langchain-text-splitters==0.3.2
langsmith==0.1.143
Levenshtein==0.26.1
litellm==1.52.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.23.1
mdurl==0.1.2
mistune==3.0.2
multidict==6.1.0
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nbsphinx==0.9.5
nest-asyncio==1.6.0
numpy==1.26.4
openai==1.54.2
orjson==3.10.11
packaging==24.2
pandas==2.2.3
pandoc==2.4
pandocfilters==1.5.1
platformdirs==4.3.6
pluggy==1.5.0
plumbum==1.9.0
ply==3.11
podcastfy==0.4.1
propcache==0.2.0
proto-plus==1.25.0
protobuf==5.28.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pydantic==2.9.2
pydantic-settings==2.6.1
pydantic_core==2.23.4
pydub==0.25.1
Pygments==2.18.0
PyJWT==2.8.0
PyMuPDF==1.24.13
pyparsing==3.2.0
pytest==8.3.3
pytest-xdist==3.6.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-engineio==4.10.1
python-Levenshtein==0.26.1
python-socketio==5.11.4
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
RapidFuzz==3.10.1
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.21.0
rsa==4.9
shapely==2.0.6
shellingham==1.5.4
simple-websocket==1.1.0
six==1.16.0
sniffio==1.3.1
snowballstemmer==2.2.0
soupsieve==2.6
Sphinx==8.1.3
sphinx-autodoc-typehints==2.5.0
sphinx-rtd-theme==3.0.2
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
SQLAlchemy==2.0.35
tenacity==9.0.0
tiktoken==0.8.0
tinycss2==1.4.0
tokenizers==0.20.3
tornado==6.4.1
tqdm==4.67.0
traitlets==5.14.3
typer==0.12.5
types-PyYAML==6.0.12.20240917
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
uritemplate==4.1.1
urllib3==2.2.3
webencodings==0.5.1
websockets==14.1
Werkzeug==3.1.3
wsproto==1.2.0
yarl==1.17.1
youtube-transcript-api==0.6.2
zipp==3.21.0

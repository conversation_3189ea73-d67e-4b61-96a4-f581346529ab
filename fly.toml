# fly.toml app configuration file generated for openpod on 2024-11-16T12:01:28-05:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'openpod'
primary_region = 'ewr'

[build]

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

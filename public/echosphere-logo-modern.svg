<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions for gradients -->
  <defs>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0DFFD8" />
      <stop offset="100%" stop-color="#00B3A0" />
    </linearGradient>
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#0DFFD8" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#0DFFD8" stop-opacity="0" />
    </radialGradient>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="5" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- Background glow -->
  <circle cx="100" cy="100" r="80" fill="url(#glowGradient)" filter="url(#glow)" />
  
  <!-- Main circular base -->
  <circle cx="100" cy="100" r="70" fill="#1A2035" stroke="url(#primaryGradient)" stroke-width="2" />
  
  <!-- Sound wave rings -->
  <circle cx="100" cy="100" r="30" fill="none" stroke="url(#primaryGradient)" stroke-width="2" stroke-opacity="0.8" />
  <circle cx="100" cy="100" r="45" fill="none" stroke="url(#primaryGradient)" stroke-width="1.5" stroke-opacity="0.6" stroke-dasharray="2 3" />
  <circle cx="100" cy="100" r="60" fill="none" stroke="url(#primaryGradient)" stroke-width="1" stroke-opacity="0.4" stroke-dasharray="1 5" />
  
  <!-- Waveform visualization -->
  <path d="M60,100 Q70,85 80,100 Q90,115 100,100 Q110,85 120,100 Q130,115 140,100" 
        stroke="url(#primaryGradient)" stroke-width="3" fill="none" stroke-linecap="round" />
  
  <!-- Microphone icon -->
  <g transform="translate(85, 75)">
    <rect x="0" y="0" width="30" height="40" rx="15" fill="url(#primaryGradient)" />
    <rect x="10" y="5" width="10" height="30" rx="5" fill="#1A2035" />
    <path d="M15,45 L15,55" stroke="url(#primaryGradient)" stroke-width="3" stroke-linecap="round" />
    <path d="M0,55 L30,55" stroke="url(#primaryGradient)" stroke-width="3" stroke-linecap="round" />
  </g>
  
  <!-- Decorative elements -->
  <circle cx="70" cy="70" r="3" fill="#0DFFD8" />
  <circle cx="130" cy="70" r="3" fill="#0DFFD8" />
  <circle cx="70" cy="130" r="3" fill="#0DFFD8" />
  <circle cx="130" cy="130" r="3" fill="#0DFFD8" />
</svg>

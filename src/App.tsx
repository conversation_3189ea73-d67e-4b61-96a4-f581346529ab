import { CustomPodcast } from "@/components/CustomPodcast";
import { TopicPodcast } from "@/components/TopicPodcast";
import { PodcastLibrary } from "@/components/PodcastLibrary";
import { ThemeToggle } from "@/components/theme-toggle";
import { StatusIndicator } from "@/components/status-indicator";
import { Toaster } from "@/components/ui/toaster";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Sparkles } from "lucide-react";

export default function App() {
  return (
    <div className="h-screen w-full bg-gradient-to-br from-background via-background to-muted/20 flex flex-col overflow-hidden">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b glass">
        <div className="container flex h-16 items-center justify-between px-4">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="relative group">
                <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary via-primary to-primary/80 flex items-center justify-center shadow-lg transition-transform group-hover:scale-105">
                  <Sparkles className="h-6 w-6 text-primary-foreground animate-pulse" />
                </div>
                <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500 border-2 border-background animate-pulse"></div>
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity blur-xl"></div>
              </div>
              <div>
                <h1 className="text-xl font-bold gradient-text">
                  OpenPod
                </h1>
                <p className="text-xs text-muted-foreground">AI Podcast Studio</p>
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-4">
            <StatusIndicator />
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder-avatar.jpg" alt="User" />
                <AvatarFallback className="text-xs">OP</AvatarFallback>
              </Avatar>
              <ThemeToggle />
            </div>
          </div>
        </div>
      </header>

      {/* Welcome Section */}
      <div className="border-b bg-gradient-to-r from-background via-muted/20 to-background relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-primary/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 py-12 relative">
          <div className="text-center space-y-6">
            <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium border border-primary/20 backdrop-blur-sm animate-fade-in-up">
              <Sparkles className="h-4 w-4 animate-pulse" />
              <span>AI-Powered Podcast Generation</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight animate-fade-in-up-delay-1">
              Welcome to <span className="gradient-text">OpenPod</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed animate-fade-in-up-delay-2">
              Transform any content into engaging podcast conversations using advanced AI.
              Create professional podcasts from URLs, topics, or custom content in minutes.
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-sm animate-fade-in-up-delay-3">
              <div className="flex items-center space-x-2 bg-background/50 backdrop-blur-sm px-3 py-2 rounded-lg border border-border/50 hover:bg-background/70 transition-colors">
                <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse"></div>
                <span className="text-muted-foreground">Multiple AI Models</span>
              </div>
              <div className="flex items-center space-x-2 bg-background/50 backdrop-blur-sm px-3 py-2 rounded-lg border border-border/50 hover:bg-background/70 transition-colors">
                <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse"></div>
                <span className="text-muted-foreground">Natural Conversations</span>
              </div>
              <div className="flex items-center space-x-2 bg-background/50 backdrop-blur-sm px-3 py-2 rounded-lg border border-border/50 hover:bg-background/70 transition-colors">
                <div className="h-2 w-2 rounded-full bg-purple-500 animate-pulse"></div>
                <span className="text-muted-foreground">Instant Generation</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - 3 Column Layout */}
      <main className="flex-1 overflow-hidden">
        <div className="h-full grid grid-cols-1 lg:grid-cols-3 gap-6 p-6 overflow-auto">
          <div className="lg:col-span-1 overflow-auto">
            <CustomPodcast />
          </div>
          <div className="lg:col-span-1 overflow-auto">
            <TopicPodcast />
          </div>
          <div className="lg:col-span-1 overflow-auto">
            <PodcastLibrary />
          </div>
        </div>
      </main>

      <Toaster />
    </div>
  );
}

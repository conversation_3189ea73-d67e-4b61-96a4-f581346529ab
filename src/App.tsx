import { CustomPodcast } from "@/components/CustomPodcast";
import { TopicPodcast } from "@/components/TopicPodcast";
import { PodcastLibrary } from "@/components/PodcastLibrary";
import { ThemeToggle } from "@/components/theme-toggle";
import { StatusIndicator } from "@/components/status-indicator";
import { Toaster } from "@/components/ui/toaster";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import { Mic, Radio, Library, Sparkles } from "lucide-react";

export default function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b glass">
        <div className="container flex h-16 items-center justify-between px-4">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="relative group">
                <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary via-primary to-primary/80 flex items-center justify-center shadow-lg transition-transform group-hover:scale-105">
                  <Sparkles className="h-6 w-6 text-primary-foreground animate-pulse" />
                </div>
                <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500 border-2 border-background animate-pulse"></div>
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity blur-xl"></div>
              </div>
              <div>
                <h1 className="text-xl font-bold gradient-text">
                  OpenPod
                </h1>
                <p className="text-xs text-muted-foreground">AI Podcast Studio</p>
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-4">
            <StatusIndicator />
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder-avatar.jpg" alt="User" />
                <AvatarFallback className="text-xs">OP</AvatarFallback>
              </Avatar>
              <ThemeToggle />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* Welcome Section */}
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tight">Welcome back!</h2>
            <p className="text-muted-foreground">
              Create engaging podcasts from any content using AI-powered tools.
            </p>
          </div>

          {/* Main Tabs */}
          <Tabs defaultValue="custom" className="space-y-8">
            <TabsList className="grid w-full grid-cols-3 lg:w-[500px] h-12 p-1 bg-muted/50 backdrop-blur-sm">
              <TabsTrigger value="custom" className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-md transition-all">
                <Mic className="h-4 w-4" />
                <span className="hidden sm:inline">Content Studio</span>
                <span className="sm:hidden">Studio</span>
              </TabsTrigger>
              <TabsTrigger value="topic" className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-md transition-all">
                <Radio className="h-4 w-4" />
                <span className="hidden sm:inline">Topic Explorer</span>
                <span className="sm:hidden">Topics</span>
              </TabsTrigger>
              <TabsTrigger value="library" className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-md transition-all">
                <Library className="h-4 w-4" />
                <span>Library</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="custom" className="space-y-4">
              <CustomPodcast />
            </TabsContent>

            <TabsContent value="topic" className="space-y-4">
              <TopicPodcast />
            </TabsContent>

            <TabsContent value="library" className="space-y-4">
              <PodcastLibrary />
            </TabsContent>
          </Tabs>
        </div>
      </main>

      <Toaster />
    </div>
  );
}

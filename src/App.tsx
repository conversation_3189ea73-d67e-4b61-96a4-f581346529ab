import { CustomPodcast } from "@/components/CustomPodcast";
import { TopicPodcast } from "@/components/TopicPodcast";
import { PodcastLibrary } from "@/components/PodcastLibrary";
import { ThemeToggle } from "@/components/theme-toggle";
import { StatusIndicator } from "@/components/status-indicator";
import { Toaster } from "@/components/ui/toaster";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Sparkles } from "lucide-react";

export default function App() {
  return (
    <div className="h-screen w-full bg-slate-950 flex flex-col overflow-hidden">
      {/* Header */}
      <header className="border-b border-slate-800 bg-slate-950/80 backdrop-blur-xl">
        <div className="flex h-16 items-center justify-between px-6">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-emerald-400 to-cyan-400 flex items-center justify-center">
              <Sparkles className="h-5 w-5 text-slate-900" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-white">OpenPod</h1>
              <p className="text-xs text-slate-400">AI Podcast Studio</p>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-4">
            <StatusIndicator />
            <Separator orientation="vertical" className="h-6 bg-slate-700" />
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder-avatar.jpg" alt="User" />
                <AvatarFallback className="text-xs bg-slate-700 text-slate-200">OP</AvatarFallback>
              </Avatar>
              <ThemeToggle />
            </div>
          </div>
        </div>
      </header>

      {/* Welcome Section */}
      <div className="border-b border-slate-800 bg-slate-900/50 relative overflow-hidden">
        <div className="px-6 py-4 relative">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-emerald-400 to-cyan-400 flex items-center justify-center">
              <Sparkles className="h-5 w-5 text-slate-900" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">Hello, John</h1>
              <p className="text-slate-400">How can I help you today?</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Modern Card Layout */}
      <main className="flex-1 overflow-hidden bg-slate-950">
        <div className="h-full grid grid-cols-1 lg:grid-cols-3 gap-6 p-6 overflow-auto">
          <div className="lg:col-span-1 overflow-auto">
            <CustomPodcast />
          </div>
          <div className="lg:col-span-1 overflow-auto">
            <TopicPodcast />
          </div>
          <div className="lg:col-span-1 overflow-auto">
            <PodcastLibrary />
          </div>
        </div>
      </main>

      <Toaster />
    </div>
  );
}

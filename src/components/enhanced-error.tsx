import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Link, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";

interface ErrorSuggestion {
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "secondary";
  };
  link?: {
    label: string;
    url: string;
  };
}

interface EnhancedErrorProps {
  title?: string;
  message: string;
  suggestions?: ErrorSuggestion[];
  onRetry?: () => void;
  retryLabel?: string;
  className?: string;
}

export function EnhancedError({
  title = "Generation Failed",
  message,
  suggestions = [],
  onRetry,
  retryLabel = "Try Again",
  className,
}: EnhancedErrorProps) {
  // Auto-generate suggestions based on common error patterns
  const autoSuggestions: ErrorSuggestion[] = [];

  if (message.toLowerCase().includes("api key")) {
    autoSuggestions.push({
      title: "Check API Keys",
      description: "Verify your API keys are valid and have sufficient quota.",
      link: {
        label: "API Settings",
        url: "#api-keys",
      },
    });
  }

  if (message.toLowerCase().includes("quota") || message.toLowerCase().includes("limit")) {
    autoSuggestions.push({
      title: "Try Alternative TTS",
      description: "Switch to Edge TTS (free) or check your API billing.",
      action: {
        label: "Use Edge TTS",
        onClick: () => {
          // This would be handled by parent component
          console.log("Switch to Edge TTS");
        },
        variant: "outline",
      },
    });
  }

  if (message.toLowerCase().includes("network") || message.toLowerCase().includes("connection")) {
    autoSuggestions.push({
      title: "Check Connection",
      description: "Verify your internet connection and try again.",
      action: {
        label: "Retry",
        onClick: onRetry || (() => {}),
        variant: "outline",
      },
    });
  }

  if (message.toLowerCase().includes("timeout")) {
    autoSuggestions.push({
      title: "Reduce Content Size",
      description: "Try with shorter content or enable long-form mode.",
      action: {
        label: "Enable Long-form",
        onClick: () => {
          console.log("Enable long-form mode");
        },
        variant: "outline",
      },
    });
  }

  const allSuggestions = [...suggestions, ...autoSuggestions];

  return (
    <div className={cn("space-y-4", className)}>
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription className="mt-2">
          {message}
        </AlertDescription>
      </Alert>

      {allSuggestions.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm font-medium">
            <HelpCircle className="h-4 w-4" />
            Suggestions
          </div>
          
          {allSuggestions.map((suggestion, index) => (
            <div
              key={index}
              className="p-4 border rounded-lg bg-muted/50 space-y-2"
            >
              <h4 className="font-medium text-sm">{suggestion.title}</h4>
              <p className="text-sm text-muted-foreground">
                {suggestion.description}
              </p>
              
              <div className="flex gap-2">
                {suggestion.action && (
                  <Button
                    size="sm"
                    variant={suggestion.action.variant || "default"}
                    onClick={suggestion.action.onClick}
                  >
                    {suggestion.action.label}
                  </Button>
                )}
                
                {suggestion.link && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(suggestion.link!.url, '_blank')}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    {suggestion.link.label}
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {onRetry && (
        <div className="flex justify-center pt-2">
          <Button onClick={onRetry} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            {retryLabel}
          </Button>
        </div>
      )}
    </div>
  );
}

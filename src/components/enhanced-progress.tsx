import { Check<PERSON>ir<PERSON>, Circle, Loader2 } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface Step {
  id: string;
  label: string;
  description?: string;
}

interface EnhancedProgressProps {
  steps: Step[];
  currentStep: number;
  progress: number;
  isLoading?: boolean;
  error?: string;
  estimatedTime?: string;
  className?: string;
}

export function EnhancedProgress({
  steps,
  currentStep,
  progress,
  isLoading = false,
  error,
  estimatedTime,
  className,
}: EnhancedProgressProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="font-medium">
            {isLoading ? "Processing..." : "Progress"}
          </span>
          <span className="text-muted-foreground">{progress}%</span>
        </div>
        <Progress value={progress} className="h-2" />
        {estimatedTime && (
          <p className="text-xs text-muted-foreground">
            Estimated time remaining: {estimatedTime}
          </p>
        )}
      </div>

      {/* Steps */}
      <div className="space-y-3">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          const isPending = index > currentStep;

          return (
            <div
              key={step.id}
              className={cn(
                "flex items-start gap-3 p-3 rounded-lg transition-colors",
                {
                  "bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800":
                    isCompleted,
                  "bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800":
                    isCurrent && !error,
                  "bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800":
                    isCurrent && error,
                  "bg-muted/50": isPending,
                }
              )}
            >
              {/* Step Icon */}
              <div className="flex-shrink-0 mt-0.5">
                {isCompleted ? (
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                ) : isCurrent ? (
                  isLoading ? (
                    <Loader2 className="h-5 w-5 text-blue-600 dark:text-blue-400 animate-spin" />
                  ) : error ? (
                    <Circle className="h-5 w-5 text-red-600 dark:text-red-400" />
                  ) : (
                    <Loader2 className="h-5 w-5 text-blue-600 dark:text-blue-400 animate-spin" />
                  )
                ) : (
                  <Circle className="h-5 w-5 text-muted-foreground" />
                )}
              </div>

              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <p
                  className={cn("font-medium", {
                    "text-green-700 dark:text-green-300": isCompleted,
                    "text-blue-700 dark:text-blue-300": isCurrent && !error,
                    "text-red-700 dark:text-red-300": isCurrent && error,
                    "text-muted-foreground": isPending,
                  })}
                >
                  {step.label}
                </p>
                {step.description && (
                  <p
                    className={cn("text-sm mt-1", {
                      "text-green-600 dark:text-green-400": isCompleted,
                      "text-blue-600 dark:text-blue-400": isCurrent && !error,
                      "text-red-600 dark:text-red-400": isCurrent && error,
                      "text-muted-foreground": isPending,
                    })}
                  >
                    {step.description}
                  </p>
                )}
                {isCurrent && error && (
                  <p className="text-sm mt-1 text-red-600 dark:text-red-400">
                    {error}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

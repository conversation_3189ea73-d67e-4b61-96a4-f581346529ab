import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Wifi, WifiOff, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface StatusIndicatorProps {
  className?: string;
}

export function StatusIndicator({ className }: StatusIndicatorProps) {
  const [status, setStatus] = useState<"connected" | "disconnected" | "error">("disconnected");
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkServerStatus = async () => {
    try {
      const response = await fetch("http://localhost:8082/api/health", {
        method: "GET",
        timeout: 5000,
      } as RequestInit);
      
      if (response.ok) {
        setStatus("connected");
      } else {
        setStatus("error");
      }
    } catch (error) {
      setStatus("disconnected");
    }
    setLastChecked(new Date());
  };

  useEffect(() => {
    // Check status immediately
    checkServerStatus();
    
    // Check status every 30 seconds
    const interval = setInterval(checkServerStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusConfig = () => {
    switch (status) {
      case "connected":
        return {
          icon: Wifi,
          text: "Online",
          variant: "default" as const,
          className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
        };
      case "error":
        return {
          icon: AlertCircle,
          text: "Issues",
          variant: "destructive" as const,
          className: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
        };
      case "disconnected":
      default:
        return {
          icon: WifiOff,
          text: "Offline",
          variant: "secondary" as const,
          className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <Badge
      variant={config.variant}
      className={cn(
        "flex items-center gap-1 text-xs font-medium",
        config.className,
        className
      )}
      title={lastChecked ? `Last checked: ${lastChecked.toLocaleTimeString()}` : "Checking..."}
    >
      <Icon className="h-3 w-3" />
      {config.text}
    </Badge>
  );
}

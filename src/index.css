@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;
    --primary: 174 80% 40%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 46.9%;
    --accent: 174 80% 40%;
    --accent-foreground: 222 47% 11%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 174 80% 40%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme */
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    --card: 222 47% 14%;
    --card-foreground: 210 40% 98%;
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 174 80% 50%;
    --primary-foreground: 222 47% 11%;
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 174 80% 50%;
    --accent-foreground: 222 47% 11%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 33% 20%;
    --input: 217 33% 20%;
    --ring: 174 80% 50%;
  }

  * {
    @apply border-border;
  }

  html, body {
    @apply bg-background text-foreground m-0 p-0 h-full w-full overflow-hidden;
  }

  #root {
    @apply h-full w-full;
  }
}
